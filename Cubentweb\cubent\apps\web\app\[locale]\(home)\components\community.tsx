'use client';

import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from '@repo/design-system/components/ui/avatar';
import { Button } from '@repo/design-system/components/ui/button';
import type { Dictionary } from '@repo/internationalization';
import { ExternalLink, Github, MessageCircle } from 'lucide-react';
import { useEffect, useState } from 'react';

type CommunityProps = {
  dictionary: Dictionary;
};

// Sample community reviews data
const communityReviews = [
  {
    id: 1,
    username: 'justingodev',
    avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
    content: 'What @cubentbase + @nextjs is amazing! 🔥 Really excited into a proof-of-concept and already have a lot of the functionality in place 🤯 🤯 🤯',
    platform: 'twitter'
  },
  {
    id: 2,
    username: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
    content: 'Using @cubentbase I\'m really impressed with the developers and see it in general. Despite being a bit dubious about the fact that I have to say I really don\'t miss anything. The whole experience feels very robust and secure.',
    platform: 'twitter'
  },
  {
    id: 3,
    username: 'joerell',
    avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face',
    content: 'This weekend I made a personal record 🏆 on the last time spent creating an application with social login / permissions, database, cdn, and for free. Thanks to @cubentbase and @vercel.',
    platform: 'twitter'
  },
  {
    id: 4,
    username: 'stevemarshall',
    avatar: 'https://images.unsplash.com/photo-1519244703995-f4e0f30006d5?w=150&h=150&fit=crop&crop=face',
    content: 'Working on my own cubent project. I want this to be my first because I\'m not a backend engineer and I\'m getting it done.',
    platform: 'twitter'
  },
  {
    id: 5,
    username: 'BraydenCoyer',
    avatar: 'https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=150&h=150&fit=crop&crop=face',
    content: 'New to @cubentbase I was really impressed with the developers and see it in general. Despite being a bit dubious about the fact that I have to say I really don\'t miss anything.',
    platform: 'twitter'
  },
  {
    id: 6,
    username: 'axaxone',
    avatar: 'https://images.unsplash.com/photo-1507591064344-4c6ce005b128?w=150&h=150&fit=crop&crop=face',
    content: 'Completed @cubentbase is it. It had the best experience I\'ve had with a database. Despite being a bit dubious about the fact that I have to say I really don\'t miss anything.',
    platform: 'twitter'
  },
  {
    id: 7,
    username: 'GenericCassel',
    avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
    content: 'Biggest @cubentbase is amazing! Really impressed with the developers and see it in general.',
    platform: 'twitter'
  }
];

export const Community = ({ dictionary }: CommunityProps) => {
  const [currentIndex, setCurrentIndex] = useState(0);

  // Auto-scroll animation
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentIndex((prevIndex) => (prevIndex + 1) % communityReviews.length);
    }, 3000);

    return () => clearInterval(interval);
  }, []);

  // Create animated columns for the animation effect
  const createAnimatedColumn = (startIndex: number, direction: 'left' | 'right') => {
    const reviews = [...communityReviews, ...communityReviews]; // Duplicate for seamless loop
    const animationClass = direction === 'left' ? 'animate-scroll-left' : 'animate-scroll-right';

    return (
      <div className={`flex gap-6 ${animationClass}`} style={{ animationDelay: `${startIndex * 0.5}s` }}>
        {reviews.map((review, index) => (
          <div
            key={`${review.id}-${index}`}
            className="flex-shrink-0 w-80 bg-gray-800/80 backdrop-blur-sm rounded-lg p-5 border border-gray-700/50 shadow-lg"
          >
            <div className="flex items-start gap-3">
              <Avatar className="h-10 w-10 flex-shrink-0">
                <AvatarImage src={review.avatar} alt={review.username} />
                <AvatarFallback>{review.username.slice(0, 2).toUpperCase()}</AvatarFallback>
              </Avatar>
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2 mb-2">
                  <span className="text-white font-medium text-sm">@{review.username}</span>
                  {/* X (Twitter) Logo with shadow */}
                  <div className="relative">
                    <svg
                      viewBox="0 0 24 24"
                      className="w-4 h-4 fill-white drop-shadow-lg"
                      style={{
                        filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.3)) drop-shadow(0 0 8px rgba(255,255,255,0.1))'
                      }}
                    >
                      <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
                    </svg>
                  </div>
                </div>
                <p className="text-gray-300 text-sm leading-relaxed">
                  {review.content}
                </p>
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  };

  return (
    <div className="w-full py-20 lg:py-40 bg-black relative overflow-hidden">
      {/* Background gradient effects */}
      <div className="absolute inset-0 bg-gradient-to-b from-transparent via-gray-900/20 to-transparent" />
      
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="flex flex-col items-center gap-10">
          {/* Header */}
          <div className="text-center">
            <h2 className="text-white font-regular text-3xl tracking-tighter md:text-5xl mb-4">
              Join the community
            </h2>
            <p className="text-gray-400 text-lg max-w-2xl mx-auto">
              Discover what our community has to say about their Cubent experience.
            </p>
          </div>

          {/* IDE Buttons */}
          <div className="flex flex-col sm:flex-row gap-4">
            <Button
              className="bg-black border border-gray-800 text-white hover:bg-gray-900 rounded-full px-6 py-3 font-medium"
              asChild
            >
              <a
                href="https://marketplace.visualstudio.com/items?itemName=cubent.cubent"
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center gap-3"
              >
                <svg className="w-5 h-5" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M23.15 2.587L18.21.21a1.494 1.494 0 0 0-1.705.29l-9.46 8.63-4.12-3.128a.999.999 0 0 0-1.276.057L.327 7.261A1 1 0 0 0 .326 8.74L3.899 12 .326 15.26a1 1 0 0 0 .001 1.479L1.65 17.94a.999.999 0 0 0 1.276.057l4.12-3.128 9.46 8.63a1.492 1.492 0 0 0 1.704.29l4.942-2.377A1.5 1.5 0 0 0 24 20.06V3.939a1.5 1.5 0 0 0-.85-1.352zm-5.146 14.861L10.826 12l7.178-5.448v10.896z"/>
                </svg>
                VS Code
              </a>
            </Button>
            <Button
              className="bg-black border border-gray-800 text-white hover:bg-gray-900 rounded-full px-6 py-3 font-medium"
              asChild
            >
              <a
                href="https://plugins.jetbrains.com/plugin/cubent"
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center gap-3"
              >
                <svg className="w-5 h-5" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M0 0v24h24V0H0zm3.723 3.111h5v1.834h-1.39v6.277h1.39v1.834h-5v-1.834h1.444V4.945H3.723V3.111zm11.055 0H17v1.834h-1.389v6.277H17v1.834h-2.222V3.111zm-8.334 8.944H9.61v1.833H6.444v-1.833z"/>
                </svg>
                JetBrains
              </a>
            </Button>
          </div>

          {/* Animated Reviews */}
          <div className="w-full max-w-7xl mx-auto">
            <div className="space-y-8 mask-gradient">
              {/* First column - scrolling left */}
              <div className="overflow-hidden">
                {createAnimatedColumn(0, 'left')}
              </div>

              {/* Second column - scrolling right */}
              <div className="overflow-hidden">
                {createAnimatedColumn(1, 'right')}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
