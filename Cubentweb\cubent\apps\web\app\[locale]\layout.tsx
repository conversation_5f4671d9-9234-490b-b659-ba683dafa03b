// Main layout component for the Cubent website
import './styles.css';
import { Toolbar as CMSToolbar } from '@repo/cms/components/toolbar';
import { DesignSystemProvider } from '@repo/design-system';
import { fonts } from '@repo/design-system/lib/fonts';
import { cn } from '@repo/design-system/lib/utils';
import { Toolbar } from '@repo/feature-flags/components/toolbar';
import { getDictionary } from '@repo/internationalization';
import type { ReactNode } from 'react';
import Link from 'next/link';
import { Footer } from './components/footer';
import { Header } from './components/header';

type RootLayoutProperties = {
  readonly children: ReactNode;
  readonly params: Promise<{
    locale: string;
  }>;
};

const RootLayout = async ({ children, params }: RootLayoutProperties) => {
  const { locale } = await params;
  const dictionary = await getDictionary(locale);

  return (
    <html
      lang={locale}
      className={cn(fonts, 'scroll-smooth')}
      suppressHydrationWarning
    >
      <body>
        <DesignSystemProvider>
          <Header dictionary={dictionary} />
          {/* Byak Plan Announcement Banner */}
          <div className="w-full bg-gray-800 border-b border-gray-700">
            <div className="container mx-auto px-4 py-2 text-center">
              <span className="text-sm text-gray-300">
                We've released Byak - our premium plan for advanced features
              </span>
              <Link
                href="/pricing"
                className="ml-2 text-sm text-gray-100 hover:text-white underline underline-offset-2 transition-colors"
              >
                Start free trial
              </Link>
            </div>
          </div>
          {children}
          <Footer />
        </DesignSystemProvider>
        <Toolbar />
        <CMSToolbar />
      </body>
    </html>
  );
};

export default RootLayout;
