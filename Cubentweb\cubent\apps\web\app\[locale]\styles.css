@import "@repo/design-system/styles/globals.css";

.shiki {
  background-color: var(--shiki-light-bg);
  color: var(--shiki-light);
  @apply border-border;
}

.shiki span {
  color: var(--shiki-light);
}

.dark .shiki {
  background-color: var(--shiki-dark-bg);
  color: var(--shiki-dark);
}

.dark .shiki span {
  color: var(--shiki-dark);
}

.shiki code {
  display: grid;
  font-size: 13px;
  counter-reset: line;
}

.shiki .line:before {
  content: counter(line);
  counter-increment: line;

  @apply inline-block w-4 mr-8 text-muted-foreground text-right;
}

.shiki[title]:before {
  content: attr(title);
  @apply inline-block text-muted-foreground text-right mb-6 text-sm;
}

/* Community section animations */
@keyframes scroll-left {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

@keyframes scroll-right {
  0% {
    transform: translateX(-50%);
  }
  100% {
    transform: translateX(0);
  }
}

.animate-scroll-left {
  animation: scroll-left 35s linear infinite;
}

.animate-scroll-right {
  animation: scroll-right 35s linear infinite;
}

.mask-gradient {
  mask-image: linear-gradient(
    to right,
    transparent 0%,
    black 15%,
    black 85%,
    transparent 100%
  );
  -webkit-mask-image: linear-gradient(
    to right,
    transparent 0%,
    black 15%,
    black 85%,
    transparent 100%
  );
}
